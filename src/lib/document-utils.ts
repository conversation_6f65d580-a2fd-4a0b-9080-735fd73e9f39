import JSZip from 'jszip';

export interface DocumentInfo {
  id: string;
  name: string;
  url: string;
  type: 'index' | 'acte';
}

/**
 * Convert Google Drive URLs to direct download URLs
 */
export const processGoogleDriveUrl = (url: string): string => {
  if (!url) return '';
  
  // Handle different Google Drive URL formats
  const driveRegex = /(?:https?:\/\/)?(?:www\.)?(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
  const match = url.match(driveRegex);
  
  if (match) {
    const fileId = match[1];
    // Use the export URL for direct PDF access
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  }
  
  // If it's already a direct URL or not a Google Drive URL, return as is
  return url;
};

/**
 * Download a single file from a URL
 */
export const downloadFile = async (url: string, filename: string): Promise<Blob> => {
  const processedUrl = processGoogleDriveUrl(url);
  
  try {
    const response = await fetch(processedUrl, {
      method: 'GET',
      mode: 'cors',
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.blob();
  } catch (error) {
    // If CORS fails, try with no-cors mode (won't get the blob but will trigger download)
    console.warn('CORS download failed, falling back to direct link:', error);
    throw new Error(`Failed to download ${filename}`);
  }
};

/**
 * Create and download a ZIP file containing multiple documents
 */
export const downloadAllDocuments = async (
  documents: DocumentInfo[],
  zipFileName: string = 'documents.zip',
  onProgress?: (current: number, total: number) => void
): Promise<void> => {
  if (documents.length === 0) {
    throw new Error('No documents to download');
  }

  const zip = new JSZip();
  let successCount = 0;
  let failedFiles: string[] = [];

  // Try to download each file and add to ZIP
  for (let i = 0; i < documents.length; i++) {
    const doc = documents[i];
    onProgress?.(i + 1, documents.length);

    try {
      const blob = await downloadFile(doc.url, doc.name);
      zip.file(`${doc.name}.pdf`, blob);
      successCount++;
    } catch (error) {
      console.error(`Failed to download ${doc.name}:`, error);
      failedFiles.push(doc.name);
      
      // Add a text file with the URL for failed downloads
      zip.file(`${doc.name}_LINK.txt`, `Failed to download automatically. Direct link: ${doc.url}`);
    }
  }

  // Generate the ZIP file
  const zipBlob = await zip.generateAsync({ type: 'blob' });
  
  // Create download link
  const link = document.createElement('a');
  link.href = URL.createObjectURL(zipBlob);
  link.download = zipFileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(link.href);

  // Return summary
  if (failedFiles.length > 0) {
    throw new Error(`Downloaded ${successCount}/${documents.length} files. Failed: ${failedFiles.join(', ')}`);
  }
};

/**
 * Download individual document with proper Google Drive handling
 */
export const downloadSingleDocument = (url: string, filename: string): void => {
  const processedUrl = processGoogleDriveUrl(url);
  
  const link = document.createElement('a');
  link.href = processedUrl;
  link.download = filename.endsWith('.pdf') ? filename : `${filename}.pdf`;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Check if a URL is accessible (for validation)
 */
export const validateDocumentUrl = async (url: string): Promise<boolean> => {
  try {
    const processedUrl = processGoogleDriveUrl(url);
    const response = await fetch(processedUrl, { 
      method: 'HEAD',
      mode: 'no-cors'
    });
    return true;
  } catch (error) {
    console.warn('URL validation failed:', error);
    return false;
  }
};
