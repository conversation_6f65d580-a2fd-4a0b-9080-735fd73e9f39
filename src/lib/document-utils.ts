import JSZip from 'jszip';

export interface DocumentInfo {
  id: string;
  name: string;
  url: string;
  type: 'index' | 'acte';
}

/**
 * Convert Google Drive URLs to direct download URLs
 */
export const processGoogleDriveUrl = (url: string): string => {
  if (!url) return '';

  // Handle different Google Drive URL formats
  const driveRegex = /(?:https?:\/\/)?(?:www\.)?(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
  const match = url.match(driveRegex);

  if (match) {
    const fileId = match[1];
    // Use the viewer URL for PDF display (better CORS support)
    return `https://drive.google.com/file/d/${fileId}/preview`;
  }

  // If it's already a direct URL or not a Google Drive URL, return as is
  return url;
};

/**
 * Get Google Drive download URL (different from viewer URL)
 */
export const getGoogleDriveDownloadUrl = (url: string): string => {
  if (!url) return '';

  const driveRegex = /(?:https?:\/\/)?(?:www\.)?(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
  const match = url.match(driveRegex);

  if (match) {
    const fileId = match[1];
    // Use the download URL for actual downloads
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  }

  return url;
};

/**
 * Download a single file from a URL with multiple fallback strategies
 */
export const downloadFile = async (url: string, filename: string): Promise<Blob> => {
  const downloadUrl = getGoogleDriveDownloadUrl(url);

  // Strategy 1: Try direct fetch with CORS
  try {
    const response = await fetch(downloadUrl, {
      method: 'GET',
      mode: 'cors',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    if (blob.size > 0) {
      return blob;
    }
    throw new Error('Empty response');
  } catch (error) {
    console.warn('CORS download failed, trying alternative methods:', error);
  }

  // Strategy 2: Try with no-cors mode (limited but sometimes works)
  try {
    const response = await fetch(downloadUrl, {
      method: 'GET',
      mode: 'no-cors',
    });

    const blob = await response.blob();
    if (blob.size > 0) {
      return blob;
    }
    throw new Error('No-cors mode failed');
  } catch (error) {
    console.warn('No-cors download failed:', error);
  }

  // Strategy 3: Try using a proxy approach (if available)
  try {
    // For Google Drive, try the preview URL and convert
    const previewUrl = processGoogleDriveUrl(url);
    const response = await fetch(previewUrl, {
      method: 'GET',
      mode: 'no-cors',
    });

    const blob = await response.blob();
    if (blob.size > 0) {
      return blob;
    }
    throw new Error('Preview URL failed');
  } catch (error) {
    console.warn('Preview URL download failed:', error);
  }

  // If all strategies fail, throw an error
  throw new Error(`Failed to download ${filename} - CORS restrictions prevent direct download`);
};

/**
 * Create and download a ZIP file containing multiple documents
 */
export const downloadAllDocuments = async (
  documents: DocumentInfo[],
  zipFileName: string = 'documents.zip',
  onProgress?: (current: number, total: number) => void
): Promise<void> => {
  if (documents.length === 0) {
    throw new Error('No documents to download');
  }

  const zip = new JSZip();
  let successCount = 0;
  let failedFiles: string[] = [];

  // Create a text file with all download links as backup
  let linksContent = 'Document Download Links (Backup)\n';
  linksContent += '==================================\n\n';
  linksContent += 'Use these links if any PDF files failed to download automatically:\n\n';

  for (let i = 0; i < documents.length; i++) {
    const doc = documents[i];
    onProgress?.(i + 1, documents.length);

    const downloadUrl = getGoogleDriveDownloadUrl(doc.url);
    linksContent += `${doc.name}:\n${downloadUrl}\n\n`;

    try {
      // Attempt to download the actual PDF file
      const blob = await downloadFile(doc.url, doc.name);

      // Verify it's a valid PDF by checking the blob type or size
      if (blob.size > 1000) { // PDFs are typically larger than 1KB
        zip.file(`${doc.name}.pdf`, blob);
        successCount++;
        console.log(`Successfully downloaded: ${doc.name}`);
      } else {
        throw new Error('Downloaded file appears to be empty or invalid');
      }
    } catch (error) {
      console.error(`Failed to download ${doc.name}:`, error);
      failedFiles.push(doc.name);

      // For failed downloads, create a shortcut file that opens the Google Drive link
      const shortcutContent = `[InternetShortcut]
URL=${downloadUrl}
IconFile=C:\\Program Files\\Internet Explorer\\iexplore.exe
IconIndex=1`;

      zip.file(`${doc.name}.url`, shortcutContent);
    }

    // Small delay to avoid overwhelming the server
    if (i < documents.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  // Add the links file as backup
  zip.file('DOWNLOAD_LINKS_BACKUP.txt', linksContent);

  // Add a README file explaining the contents
  const readmeContent = `Document Archive Contents
========================

This archive contains ${successCount} PDF files and ${failedFiles.length} shortcut files.

PDF Files: ${successCount}
- These are the actual document files that were successfully downloaded.

Shortcut Files (.url): ${failedFiles.length}
- These are shortcuts to documents that couldn't be downloaded automatically.
- Double-click these files to open the documents in your browser.

Failed Downloads: ${failedFiles.join(', ')}

For manual download, see DOWNLOAD_LINKS_BACKUP.txt

Generated on: ${new Date().toLocaleString()}
`;

  zip.file('README.txt', readmeContent);

  // Generate the ZIP file with compression
  const zipBlob = await zip.generateAsync({
    type: 'blob',
    compression: 'DEFLATE',
    compressionOptions: {
      level: 6
    }
  });

  // Create download link
  const link = document.createElement('a');
  link.href = URL.createObjectURL(zipBlob);
  link.download = zipFileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up
  URL.revokeObjectURL(link.href);

  // Provide feedback about the download
  if (failedFiles.length > 0) {
    throw new Error(`Archive created with ${successCount} PDF files and ${failedFiles.length} shortcuts. Some files couldn't be downloaded due to access restrictions.`);
  }
};

/**
 * Download individual document with proper Google Drive handling
 */
export const downloadSingleDocument = (url: string, filename: string): void => {
  const downloadUrl = getGoogleDriveDownloadUrl(url);

  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename.endsWith('.pdf') ? filename : `${filename}.pdf`;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Check if a URL is accessible (for validation)
 */
export const validateDocumentUrl = async (url: string): Promise<boolean> => {
  try {
    const processedUrl = processGoogleDriveUrl(url);
    const response = await fetch(processedUrl, {
      method: 'HEAD',
      mode: 'no-cors'
    });
    return true;
  } catch (error) {
    console.warn('URL validation failed:', error);
    return false;
  }
};
