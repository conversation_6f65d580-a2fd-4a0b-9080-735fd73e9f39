import JSZip from 'jszip';

export interface DocumentInfo {
  id: string;
  name: string;
  url: string;
  type: 'index' | 'acte';
}

/**
 * Convert Google Drive URLs to direct download URLs
 */
export const processGoogleDriveUrl = (url: string): string => {
  if (!url) return '';

  // Handle different Google Drive URL formats
  const driveRegex = /(?:https?:\/\/)?(?:www\.)?(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
  const match = url.match(driveRegex);

  if (match) {
    const fileId = match[1];
    // Use the viewer URL for PDF display (better CORS support)
    return `https://drive.google.com/file/d/${fileId}/preview`;
  }

  // If it's already a direct URL or not a Google Drive URL, return as is
  return url;
};

/**
 * Get Google Drive download URL (different from viewer URL)
 */
export const getGoogleDriveDownloadUrl = (url: string): string => {
  if (!url) return '';

  const driveRegex = /(?:https?:\/\/)?(?:www\.)?(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
  const match = url.match(driveRegex);

  if (match) {
    const fileId = match[1];
    // Use the download URL for actual downloads
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  }

  return url;
};

/**
 * Download a single file from a URL with multiple fallback strategies
 */
export const downloadFile = async (url: string, filename: string): Promise<Blob> => {
  const downloadUrl = getGoogleDriveDownloadUrl(url);

  // Strategy 1: Try direct fetch with CORS
  try {
    const response = await fetch(downloadUrl, {
      method: 'GET',
      mode: 'cors',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    if (blob.size > 0) {
      return blob;
    }
    throw new Error('Empty response');
  } catch (error) {
    console.warn('CORS download failed, trying alternative methods:', error);
  }

  // Strategy 2: Try with no-cors mode (limited but sometimes works)
  try {
    const response = await fetch(downloadUrl, {
      method: 'GET',
      mode: 'no-cors',
    });

    const blob = await response.blob();
    if (blob.size > 0) {
      return blob;
    }
    throw new Error('No-cors mode failed');
  } catch (error) {
    console.warn('No-cors download failed:', error);
  }

  // Strategy 3: Try using a proxy approach (if available)
  try {
    // For Google Drive, try the preview URL and convert
    const previewUrl = processGoogleDriveUrl(url);
    const response = await fetch(previewUrl, {
      method: 'GET',
      mode: 'no-cors',
    });

    const blob = await response.blob();
    if (blob.size > 0) {
      return blob;
    }
    throw new Error('Preview URL failed');
  } catch (error) {
    console.warn('Preview URL download failed:', error);
  }

  // If all strategies fail, throw an error
  throw new Error(`Failed to download ${filename} - CORS restrictions prevent direct download`);
};

/**
 * Download all documents using multiple simultaneous downloads (fallback for CORS issues)
 */
export const downloadAllDocumentsMultiple = async (
  documents: DocumentInfo[],
  onProgress?: (current: number, total: number) => void
): Promise<void> => {
  if (documents.length === 0) {
    throw new Error('No documents to download');
  }

  // Launch all downloads simultaneously
  documents.forEach((doc, index) => {
    setTimeout(() => {
      downloadSingleDocument(doc.url, doc.name);
      onProgress?.(index + 1, documents.length);
    }, index * 500); // Stagger downloads by 500ms to avoid overwhelming the browser
  });
};

/**
 * Create and download a ZIP file containing multiple documents
 * Falls back to multiple downloads if ZIP creation fails
 */
export const downloadAllDocuments = async (
  documents: DocumentInfo[],
  zipFileName: string = 'documents.zip',
  onProgress?: (current: number, total: number) => void
): Promise<void> => {
  if (documents.length === 0) {
    throw new Error('No documents to download');
  }

  // Try ZIP approach first, but with a quick timeout
  try {
    const zip = new JSZip();
    let successCount = 0;
    let failedFiles: string[] = [];

    // Attempt to download a few files to test if ZIP approach works
    const testLimit = Math.min(2, documents.length);

    for (let i = 0; i < testLimit; i++) {
      const doc = documents[i];
      onProgress?.(i + 1, documents.length);

      try {
        // Quick timeout for testing
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

        const downloadUrl = getGoogleDriveDownloadUrl(doc.url);
        const response = await fetch(downloadUrl, {
          method: 'GET',
          mode: 'cors',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();
        if (blob.size > 1000) { // PDFs are typically larger than 1KB
          zip.file(`${doc.name}.pdf`, blob);
          successCount++;
        } else {
          throw new Error('Downloaded file appears to be empty or invalid');
        }
      } catch (error) {
        console.warn(`Failed to download ${doc.name} for ZIP:`, error);
        failedFiles.push(doc.name);
        // If we can't download even the test files, fall back to multiple downloads
        if (i === 0) {
          throw new Error('ZIP approach failed, falling back to multiple downloads');
        }
      }
    }

    // If test downloads worked, continue with remaining files
    if (successCount > 0 && testLimit < documents.length) {
      for (let i = testLimit; i < documents.length; i++) {
        const doc = documents[i];
        onProgress?.(i + 1, documents.length);

        try {
          const blob = await downloadFile(doc.url, doc.name);
          if (blob.size > 1000) {
            zip.file(`${doc.name}.pdf`, blob);
            successCount++;
          } else {
            throw new Error('Downloaded file appears to be empty or invalid');
          }
        } catch (error) {
          console.error(`Failed to download ${doc.name}:`, error);
          failedFiles.push(doc.name);
        }

        // Small delay between downloads
        if (i < documents.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }

    // If we got some files, create the ZIP
    if (successCount > 0) {
      // Add a README file
      const readmeContent = `Document Archive
===============

This archive contains ${successCount} PDF files out of ${documents.length} total documents.

${failedFiles.length > 0 ? `Failed downloads: ${failedFiles.join(', ')}` : 'All documents downloaded successfully!'}

Generated on: ${new Date().toLocaleString()}
`;

      zip.file('README.txt', readmeContent);

      // Generate and download the ZIP file
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(zipBlob);
      link.download = zipFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);

      if (failedFiles.length > 0) {
        throw new Error(`ZIP created with ${successCount}/${documents.length} files. Some files couldn't be downloaded due to access restrictions.`);
      }
      return;
    } else {
      throw new Error('No files could be downloaded for ZIP creation');
    }

  } catch (error) {
    console.warn('ZIP approach failed, falling back to multiple downloads:', error);

    // Fallback: Launch multiple downloads
    await downloadAllDocumentsMultiple(documents, onProgress);
    throw new Error(`Launched ${documents.length} individual downloads. Check your Downloads folder.`);
  }
};

/**
 * Download individual document with proper Google Drive handling
 */
export const downloadSingleDocument = (url: string, filename: string): void => {
  const downloadUrl = getGoogleDriveDownloadUrl(url);

  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename.endsWith('.pdf') ? filename : `${filename}.pdf`;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Check if a URL is accessible (for validation)
 */
export const validateDocumentUrl = async (url: string): Promise<boolean> => {
  try {
    const processedUrl = processGoogleDriveUrl(url);
    const response = await fetch(processedUrl, {
      method: 'HEAD',
      mode: 'no-cors'
    });
    return true;
  } catch (error) {
    console.warn('URL validation failed:', error);
    return false;
  }
};
