import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { Loader2, AlertCircle, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';

interface PdfViewerProps {
  fileUrl: string;
  fileName?: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl, fileName = 'document.pdf' }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processedUrl, setProcessedUrl] = useState<string>('');
  const { toast } = useToast();

  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: (defaultTabs) => [
      defaultTabs[0], // Thumbnails
      defaultTabs[1], // Bookmarks
    ],
  });

  // Convert Google Drive URLs to direct download URLs
  const processGoogleDriveUrl = (url: string): string => {
    if (!url) return '';

    // Handle different Google Drive URL formats
    const driveRegex = /(?:https?:\/\/)?(?:www\.)?(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
    const match = url.match(driveRegex);

    if (match) {
      const fileId = match[1];
      // Use the export URL for direct PDF access
      return `https://drive.google.com/uc?export=download&id=${fileId}`;
    }

    // If it's already a direct URL or not a Google Drive URL, return as is
    return url;
  };

  useEffect(() => {
    if (fileUrl) {
      setIsLoading(true);
      setError(null);

      const processed = processGoogleDriveUrl(fileUrl);
      setProcessedUrl(processed);

      // Test if the URL is accessible
      const testUrl = async () => {
        try {
          await fetch(processed, {
            method: 'HEAD',
            mode: 'no-cors' // This helps with CORS issues
          });
          setIsLoading(false);
        } catch (err) {
          console.warn('Direct access failed, trying alternative approach:', err);
          // If direct access fails, try iframe approach
          setProcessedUrl(fileUrl);
          setIsLoading(false);
        }
      };

      testUrl();
    }
  }, [fileUrl]);

  const handleDownload = async () => {
    try {
      const downloadUrl = processGoogleDriveUrl(fileUrl);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Téléchargement démarré',
        description: `Le téléchargement de ${fileName} a commencé.`,
      });
    } catch (error) {
      toast({
        title: 'Erreur de téléchargement',
        description: 'Impossible de télécharger le fichier. Veuillez réessayer.',
        variant: 'destructive',
      });
    }
  };

  if (!fileUrl) {
    return (
      <div className="h-[600px] w-full flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Aucun document sélectionné</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-[600px] w-full flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-sm text-gray-600 dark:text-gray-400">Chargement du document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-[600px] w-full flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <Button variant="outline" onClick={() => window.open(fileUrl, '_blank')}>
            Ouvrir dans un nouvel onglet
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[600px] w-full border rounded-lg overflow-hidden bg-white dark:bg-gray-900">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {fileName}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-1" />
            Télécharger
          </Button>
          <Button variant="ghost" size="sm" onClick={() => window.open(fileUrl, '_blank')}>
            Ouvrir dans un nouvel onglet
          </Button>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="h-[calc(100%-60px)]">
        <Worker workerUrl="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js">
          <Viewer
            fileUrl={processedUrl}
            plugins={[defaultLayoutPluginInstance]}
            onDocumentLoad={() => setIsLoading(false)}
          />
        </Worker>
      </div>
    </div>
  );
};

export default PdfViewer;
