import React, { useState } from 'react';
import { FolderOpen, Download, Eye, FileText, Package, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import PdfViewer from '@/components/ui/pdf-viewer';
import { downloadAllDocuments, downloadSingleDocument, type DocumentInfo } from '@/lib/document-utils';

interface Document extends DocumentInfo {
  source?: any;
}

interface RequestDocumentsSectionProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  selectedPdfUrl: string | null;
  onPdfSelect: (url: string) => void;
}

const RequestDocumentsSection: React.FC<RequestDocumentsSectionProps> = ({
  indexEntries,
  actesByIndex,
  selectedPdfUrl,
  onPdfSelect
}) => {
  const [isDownloadingAll, setIsDownloadingAll] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<{ current: number; total: number } | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const { toast } = useToast();

  // Combine all documents into a single array
  const allDocuments: Document[] = [
    ...indexEntries
      .filter(index => index.doc_url)
      .map(index => ({
        id: index.id,
        name: index.doc_number ? `Index ${index.doc_number}` : `Index ${index.id.slice(0, 8)}`,
        url: index.doc_url,
        type: 'index' as const,
        source: index
      })),
    ...Object.values(actesByIndex)
      .flat()
      .filter(acte => acte.doc_url)
      .map(acte => ({
        id: acte.id,
        name: acte.acte_publication_number ? `Acte ${acte.acte_publication_number}` : `Acte ${acte.id.slice(0, 8)}`,
        url: acte.doc_url,
        type: 'acte' as const,
        source: acte
      }))
  ];

  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
    onPdfSelect(document.url);
  };

  const handleDownloadAll = async () => {
    if (allDocuments.length === 0) {
      toast({
        title: 'Aucun document',
        description: 'Il n\'y a aucun document à télécharger.',
        variant: 'destructive',
      });
      return;
    }

    setIsDownloadingAll(true);
    setDownloadProgress({ current: 0, total: allDocuments.length });

    try {
      await downloadAllDocuments(
        allDocuments,
        'documents-request.zip',
        (current, total) => {
          setDownloadProgress({ current, total });
        }
      );

      toast({
        title: 'Téléchargement terminé',
        description: `Archive ZIP avec ${allDocuments.length} documents téléchargée avec succès.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Une erreur est survenue';
      toast({
        title: 'Erreur de téléchargement',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsDownloadingAll(false);
      setDownloadProgress(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold flex items-center">
          <FolderOpen className="h-6 w-6 mr-2 text-primary" />
          Documents ({allDocuments.length})
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadAll}
          disabled={isDownloadingAll || allDocuments.length === 0}
        >
          {isDownloadingAll ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {downloadProgress && `${downloadProgress.current}/${downloadProgress.total}`}
            </>
          ) : (
            <>
              <Package className="h-4 w-4 mr-2" />
              Télécharger tout
            </>
          )}
        </Button>
      </div>

      {/* Document List - Organized by Index */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border">
        <div className="p-3 border-b">
          <h3 className="text-sm font-semibold flex items-center">
            <FileText className="h-4 w-4 mr-2 text-primary" />
            Documents par Index
          </h3>
        </div>
        <div className="p-3">
          {indexEntries.length > 0 ? (
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {indexEntries.map((index, indexIdx) => {
                const indexDoc = index.doc_url ? {
                  id: index.id,
                  name: index.doc_number ? `Index ${index.doc_number}` : `Index ${index.id.slice(0, 8)}`,
                  url: index.doc_url,
                  type: 'index' as const,
                  source: index
                } : null;

                const relatedActes = actesByIndex[index.id] || [];
                const actesDocs = relatedActes
                  .filter(acte => acte.doc_url)
                  .map(acte => ({
                    id: acte.id,
                    name: acte.acte_publication_number ? `Acte ${acte.acte_publication_number}` : `Acte ${acte.id.slice(0, 8)}`,
                    url: acte.doc_url,
                    type: 'acte' as const,
                    source: acte
                  }));

                const hasDocuments = indexDoc || actesDocs.length > 0;

                if (!hasDocuments) return null;

                return (
                  <div key={index.id} className="border rounded-lg p-3 bg-gray-50 dark:bg-gray-700/50">
                    {/* Index Header */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center font-medium">
                          {indexIdx + 1}
                        </div>
                        <span className="text-sm font-medium">
                          Index {index.lot_number || index.doc_number || (indexIdx + 1)}
                        </span>
                        {index.cadastre && (
                          <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full">
                            {index.cadastre}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Documents for this Index */}
                    <div className="space-y-1">
                      {/* Index Document */}
                      {indexDoc && (
                        <div
                          className={`flex items-center justify-between p-2 rounded border cursor-pointer transition-colors ${
                            selectedDocument?.id === indexDoc.id
                              ? 'bg-primary/10 border-primary'
                              : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                          onClick={() => handleDocumentSelect(indexDoc)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 rounded-full bg-blue-500" />
                            <span className="text-xs font-medium">{indexDoc.name}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                downloadSingleDocument(indexDoc.url, indexDoc.name);
                                toast({
                                  title: 'Téléchargement démarré',
                                  description: `Le téléchargement de ${indexDoc.name} a commencé.`,
                                });
                              }}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      )}

                      {/* Related Actes */}
                      {actesDocs.map((acteDoc) => (
                        <div
                          key={acteDoc.id}
                          className={`flex items-center justify-between p-2 rounded border cursor-pointer transition-colors ml-4 ${
                            selectedDocument?.id === acteDoc.id
                              ? 'bg-primary/10 border-primary'
                              : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                          onClick={() => handleDocumentSelect(acteDoc)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 rounded-full bg-green-500" />
                            <span className="text-xs">{acteDoc.name}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                downloadSingleDocument(acteDoc.url, acteDoc.name);
                                toast({
                                  title: 'Téléchargement démarré',
                                  description: `Le téléchargement de ${acteDoc.name} a commencé.`,
                                });
                              }}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-6">
              <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-xs text-gray-500">Aucun document disponible</p>
            </div>
          )}
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border">
        <div className="p-3 border-b">
          <h3 className="text-sm font-semibold flex items-center">
            <Eye className="h-4 w-4 mr-2 text-primary" />
            Visionneuse
            {selectedDocument && (
              <span className="ml-2 text-xs font-normal text-gray-500 truncate">
                - {selectedDocument.name}
              </span>
            )}
          </h3>
        </div>
        <div className="p-3">
          {selectedPdfUrl && selectedDocument ? (
            <div className="h-96">
              <PdfViewer
                fileUrl={selectedPdfUrl}
                fileName={selectedDocument.name}
              />
            </div>
          ) : (
            <div className="text-center py-8">
              <FolderOpen className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                Visionneuse de documents
              </h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                Sélectionnez un document pour le visualiser.
              </p>
              <Button variant="outline" size="sm" disabled>
                <Eye className="h-3 w-3 mr-1" />
                <span className="text-xs">Aucun document sélectionné</span>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequestDocumentsSection;
