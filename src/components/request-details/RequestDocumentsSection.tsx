import React, { useState } from 'react';
import { FolderOpen, Download, Eye, FileText, Package, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import PdfViewer from '@/components/ui/pdf-viewer';
import { downloadAllDocuments, downloadSingleDocument, type DocumentInfo } from '@/lib/document-utils';

interface Document extends DocumentInfo {
  source?: any;
}

interface RequestDocumentsSectionProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  selectedPdfUrl: string | null;
  onPdfSelect: (url: string) => void;
}

const RequestDocumentsSection: React.FC<RequestDocumentsSectionProps> = ({
  indexEntries,
  actesByIndex,
  selectedPdfUrl,
  onPdfSelect
}) => {
  const [isDownloadingAll, setIsDownloadingAll] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<{ current: number; total: number } | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const { toast } = useToast();

  // Combine all documents into a single array
  const allDocuments: Document[] = [
    ...indexEntries
      .filter(index => index.doc_url)
      .map(index => ({
        id: index.id,
        name: index.doc_number ? `Index ${index.doc_number}` : `Index ${index.id.slice(0, 8)}`,
        url: index.doc_url,
        type: 'index' as const,
        source: index
      })),
    ...Object.values(actesByIndex)
      .flat()
      .filter(acte => acte.doc_url)
      .map(acte => ({
        id: acte.id,
        name: acte.acte_publication_number ? `Acte ${acte.acte_publication_number}` : `Acte ${acte.id.slice(0, 8)}`,
        url: acte.doc_url,
        type: 'acte' as const,
        source: acte
      }))
  ];

  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
    onPdfSelect(document.url);
  };

  const handleDownloadAll = async () => {
    if (allDocuments.length === 0) {
      toast({
        title: 'Aucun document',
        description: 'Il n\'y a aucun document à télécharger.',
        variant: 'destructive',
      });
      return;
    }

    setIsDownloadingAll(true);
    setDownloadProgress({ current: 0, total: allDocuments.length });

    try {
      await downloadAllDocuments(
        allDocuments,
        'documents-request.zip',
        (current, total) => {
          setDownloadProgress({ current, total });
        }
      );

      toast({
        title: 'Téléchargement terminé',
        description: `Archive ZIP avec ${allDocuments.length} documents téléchargée avec succès.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Une erreur est survenue';
      toast({
        title: 'Erreur de téléchargement',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsDownloadingAll(false);
      setDownloadProgress(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold flex items-center">
          <FolderOpen className="h-6 w-6 mr-2 text-primary" />
          Documents ({allDocuments.length})
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadAll}
          disabled={isDownloadingAll || allDocuments.length === 0}
        >
          {isDownloadingAll ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {downloadProgress && `${downloadProgress.current}/${downloadProgress.total}`}
            </>
          ) : (
            <>
              <Package className="h-4 w-4 mr-2" />
              Télécharger tout
            </>
          )}
        </Button>
      </div>

      {/* Document List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold flex items-center">
            <FileText className="h-5 w-5 mr-2 text-primary" />
            Liste des documents
          </h3>
        </div>
        <div className="p-4">
          {allDocuments.length > 0 ? (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {allDocuments.map((doc) => (
                <div
                  key={doc.id}
                  className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedDocument?.id === doc.id
                      ? 'bg-primary/10 border-primary'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => handleDocumentSelect(doc)}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      doc.type === 'index' ? 'bg-blue-500' : 'bg-green-500'
                    }`} />
                    <div>
                      <p className="text-sm font-medium">{doc.name}</p>
                      <p className="text-xs text-gray-500 capitalize">{doc.type}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadSingleDocument(doc.url, doc.name);
                        toast({
                          title: 'Téléchargement démarré',
                          description: `Le téléchargement de ${doc.name} a commencé.`,
                        });
                      }}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Aucun document disponible</p>
            </div>
          )}
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold flex items-center">
            <Eye className="h-5 w-5 mr-2 text-primary" />
            Visionneuse de documents
            {selectedDocument && (
              <span className="ml-2 text-sm font-normal text-gray-500">
                - {selectedDocument.name}
              </span>
            )}
          </h3>
        </div>
        <div className="p-4">
          {selectedPdfUrl && selectedDocument ? (
            <PdfViewer
              fileUrl={selectedPdfUrl}
              fileName={selectedDocument.name}
            />
          ) : (
            <div className="text-center py-16">
              <FolderOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                Visionneuse de documents
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Sélectionnez un document dans la liste ci-dessus pour le visualiser.
              </p>
              <Button variant="outline" disabled>
                <Eye className="h-4 w-4 mr-2" />
                Aucun document sélectionné
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequestDocumentsSection;
