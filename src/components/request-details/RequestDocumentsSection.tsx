import React from 'react';
import { FolderOpen, Download, Eye, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PdfViewer from '@/components/ui/pdf-viewer';

interface RequestDocumentsSectionProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  selectedPdfUrl: string | null;
  onPdfSelect: (url: string) => void;
}

const RequestDocumentsSection: React.FC<RequestDocumentsSectionProps> = ({
  indexEntries,
  actesByIndex,
  selectedPdfUrl,
  onPdfSelect
}) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold flex items-center">
          <FolderOpen className="h-6 w-6 mr-2 text-primary" />
          Documents
        </h2>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Télécharger tout
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            Index ({indexEntries.length})
          </h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {indexEntries.length > 0 ? (
              indexEntries.slice(0, 5).map((index, idx) => (
                <div key={idx} className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                  <span className="text-sm">{index.doc_number || `Document ${idx + 1}`}</span>
                  <Button variant="ghost" size="sm" onClick={() => onPdfSelect(index.file_url)}>
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">Aucun index trouvé</p>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-green-600" />
            Actes ({Object.values(actesByIndex).flat().length})
          </h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {Object.values(actesByIndex).flat().length > 0 ? (
              Object.values(actesByIndex).flat().slice(0, 5).map((acte, idx) => (
                <div key={idx} className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                  <span className="text-sm">{acte.acte_type || `Acte ${idx + 1}`}</span>
                  <Button variant="ghost" size="sm" onClick={() => onPdfSelect(acte.file_url)}>
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">Aucun acte trouvé</p>
            )}
          </div>
        </div>
      </div>

      <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed">
        {selectedPdfUrl ? (
          <PdfViewer fileUrl={selectedPdfUrl} />
        ) : (
          <>
            <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Visionneuse de documents
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Sélectionnez un document pour le visualiser.
            </p>
            <Button variant="outline" disabled>
              <Eye className="h-4 w-4 mr-2" />
              Ouvrir la visionneuse
            </Button>
          </>
        )}
      </div>
    </div>
  );
};

export default RequestDocumentsSection;
