import React, { useState } from 'react';
import { FolderOpen, Download, FileText, Package, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { downloadAllDocuments, downloadSingleDocument, type DocumentInfo } from '@/lib/document-utils';

interface Document extends DocumentInfo {
  source?: any;
}

interface DocumentsListProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  selectedPdfUrl: string | null;
  onPdfSelect: (url: string) => void;
  onDocumentSelect?: (document: Document) => void;
}

const DocumentsList: React.FC<DocumentsListProps> = ({
  indexEntries,
  actesByIndex,
  selectedPdfUrl,
  onPdfSelect,
  onDocumentSelect
}) => {
  const [isDownloadingAll, setIsDownloadingAll] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<{ current: number; total: number } | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const { toast } = useToast();

  // Combine all documents into a single array
  const allDocuments: Document[] = [
    ...indexEntries
      .filter(index => index.doc_url)
      .map(index => ({
        id: index.id,
        name: index.doc_number ? `Index ${index.doc_number}` : `Index ${index.id.slice(0, 8)}`,
        url: index.doc_url,
        type: 'index' as const,
        source: index
      })),
    ...Object.values(actesByIndex)
      .flat()
      .filter(acte => acte.doc_url)
      .map(acte => ({
        id: acte.id,
        name: acte.acte_publication_number ? `Acte ${acte.acte_publication_number}` : `Acte ${acte.id.slice(0, 8)}`,
        url: acte.doc_url,
        type: 'acte' as const,
        source: acte
      }))
  ];

  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
    onPdfSelect(document.url);
    onDocumentSelect?.(document);
  };

  const handleDownloadAll = async () => {
    if (allDocuments.length === 0) {
      toast({
        title: 'Aucun document',
        description: 'Il n\'y a aucun document à télécharger.',
        variant: 'destructive',
      });
      return;
    }

    setIsDownloadingAll(true);
    setDownloadProgress({ current: 0, total: allDocuments.length });
    
    try {
      await downloadAllDocuments(
        allDocuments,
        'documents-request.zip',
        (current, total) => {
          setDownloadProgress({ current, total });
        }
      );

      toast({
        title: 'Archive créée',
        description: `Archive ZIP avec ${allDocuments.length} documents créée avec succès.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Une erreur est survenue';
      toast({
        title: 'Archive créée avec avertissements',
        description: errorMessage,
        variant: 'default',
      });
    } finally {
      setIsDownloadingAll(false);
      setDownloadProgress(null);
    }
  };

  return (
    <div className="space-y-3">
      {/* Header with Download All */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold flex items-center">
          <FileText className="h-4 w-4 mr-2 text-primary" />
          Documents ({allDocuments.length})
        </h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleDownloadAll}
          disabled={isDownloadingAll || allDocuments.length === 0}
          className="h-7 px-2"
        >
          {isDownloadingAll ? (
            <>
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              <span className="text-xs">
                {downloadProgress && `${downloadProgress.current}/${downloadProgress.total}`}
              </span>
            </>
          ) : (
            <>
              <Package className="h-3 w-3 mr-1" />
              <span className="text-xs">ZIP</span>
            </>
          )}
        </Button>
      </div>

      {/* Document List */}
      <div className="space-y-2 max-h-80 overflow-y-auto">
        {indexEntries.length > 0 ? (
          indexEntries.map((index, indexIdx) => {
            const indexDoc = index.doc_url ? {
              id: index.id,
              name: index.doc_number ? `Index ${index.doc_number}` : `Index ${index.id.slice(0, 8)}`,
              url: index.doc_url,
              type: 'index' as const,
              source: index
            } : null;

            const relatedActes = actesByIndex[index.id] || [];
            const actesDocs = relatedActes
              .filter(acte => acte.doc_url)
              .map(acte => ({
                id: acte.id,
                name: acte.acte_publication_number ? `Acte ${acte.acte_publication_number}` : `Acte ${acte.id.slice(0, 8)}`,
                url: acte.doc_url,
                type: 'acte' as const,
                source: acte
              }));

            const hasDocuments = indexDoc || actesDocs.length > 0;

            if (!hasDocuments) return null;

            return (
              <div key={index.id} className="space-y-1">
                {/* Consolidated Index Document Row */}
                {indexDoc && (
                  <div 
                    className={`flex items-center justify-between p-2 rounded border cursor-pointer transition-colors ${
                      selectedDocument?.id === indexDoc.id 
                        ? 'bg-primary/10 border-primary' 
                        : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => handleDocumentSelect(indexDoc)}
                  >
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center font-medium">
                        {indexIdx + 1}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-medium">
                          Index {index.lot_number || index.doc_number || (indexIdx + 1)}
                        </span>
                        {index.cadastre && (
                          <span className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full">
                            {index.cadastre}
                          </span>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadSingleDocument(indexDoc.url, indexDoc.name);
                        toast({
                          title: 'Téléchargement démarré',
                          description: `Le téléchargement de ${indexDoc.name} a commencé.`,
                        });
                      }}
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                )}

                {/* Related Actes */}
                {actesDocs.map((acteDoc) => (
                  <div 
                    key={acteDoc.id}
                    className={`flex items-center justify-between p-2 rounded border cursor-pointer transition-colors ml-6 ${
                      selectedDocument?.id === acteDoc.id 
                        ? 'bg-primary/10 border-primary' 
                        : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => handleDocumentSelect(acteDoc)}
                  >
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-green-500" />
                      <span className="text-xs">{acteDoc.name}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadSingleDocument(acteDoc.url, acteDoc.name);
                        toast({
                          title: 'Téléchargement démarré',
                          description: `Le téléchargement de ${acteDoc.name} a commencé.`,
                        });
                      }}
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            );
          })
        ) : (
          <div className="text-center py-6">
            <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-xs text-gray-500">Aucun document disponible</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentsList;
